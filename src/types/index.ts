export interface NavItem {
  title: string;
  icon: string;
  path: string;
  active?: boolean;
}

export interface TestProfile {
  id: string;
  name: string;
  description: string;
}

export interface AlertItem {
  id: string;
  message: string;
  type: 'info' | 'warning' | 'error';
  timestamp: string;
}

export interface EnvironmentalParameter {
  name: string;
  value: number;
  unit: string;
  min: number;
  max: number;
  status: 'normal' | 'warning' | 'critical';
}

export interface IVDataPoint {
  voltage: number;
  current: number;
}

export interface ModuleImage {
  id: string;
  type: 'EL' | 'IR';
  url: string;
  timestamp: string;
  defects: {
    x: number;
    y: number;
    severity: 'low' | 'medium' | 'high';
    type: string;
  }[];
}