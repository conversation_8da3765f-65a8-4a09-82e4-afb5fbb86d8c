import React from 'react';
import Card from '../components/Card';
import IVCurveChart from '../components/charts/IVCurveChart';
import ImageGallery from '../components/ImageGallery';
import TestControls from '../components/TestControls';
import AlertsPanel from '../components/AlertsPanel';
import EnvironmentalParameters from '../components/EnvironmentalParameters';
import { 
  ivCurveData, 
  moduleImages, 
  testProfiles, 
  alerts, 
  envParameters,
  temperatureHistory,
  humidityHistory
} from '../mockData';
import { DownloadCloud, RefreshCw, ChevronRight, Filter } from 'lucide-react';
import Button from '../components/Button';

const Dashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Solar PV Module Testing</h1>
        <div className="text-sm text-gray-500">Last updated: May 15, 2025 10:30 AM</div>
      </div>
      
      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2">
          <Card 
            title="I-V Curve Analysis" 
            headerRight={
              <div className="flex items-center space-x-2">
                <button className="text-gray-500 hover:text-gray-700">
                  <RefreshCw size={14} />
                </button>
                <button className="text-gray-500 hover:text-gray-700">
                  <DownloadCloud size={14} />
                </button>
              </div>
            }
          >
            <IVCurveChart data={ivCurveData} height={350} />
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card title="Test Controls">
            <TestControls profiles={testProfiles} />
          </Card>
          
          <Card 
            title="Alerts & Warnings" 
            headerRight={
              <div className="flex items-center space-x-2">
                <button className="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                  View All <ChevronRight size={14} />
                </button>
              </div>
            }
          >
            <AlertsPanel alerts={alerts} />
          </Card>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <Card 
          title="Environmental Chamber Parameters" 
          headerRight={
            <Button variant="secondary\" size="sm\" icon={<Filter size={16} />}>
              Configure Parameters
            </Button>
          }
        >
          <EnvironmentalParameters 
            parameters={envParameters} 
            temperatureHistory={temperatureHistory}
            humidityHistory={humidityHistory}
          />
        </Card>
      </div>
      
      <div>
        <Card 
          title="Module Imaging Analysis" 
          headerRight={
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">8 images total</span>
              <button className="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                View All <ChevronRight size={14} />
              </button>
            </div>
          }
        >
          <ImageGallery images={moduleImages} />
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;