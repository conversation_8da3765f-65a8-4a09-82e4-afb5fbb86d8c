import { AlertItem, EnvironmentalParameter, IVDataPoint, ModuleImage, TestProfile } from '../types';

export const testProfiles: TestProfile[] = [
  {
    id: '1',
    name: 'Standard Test Conditions (STC)',
    description: 'Test at 25°C, 1000 W/m² irradiance, AM1.5G spectrum',
  },
  {
    id: '2',
    name: 'Nominal Operating Cell Temperature (NOCT)',
    description: 'Test at 20°C ambient, 800 W/m² irradiance, 1 m/s wind speed',
  },
  {
    id: '3',
    name: 'Low Irradiance',
    description: 'Test at 25°C, 200 W/m² irradiance',
  },
  {
    id: '4',
    name: 'Temperature Coefficient',
    description: 'Multiple temperature points from 15°C to 75°C',
  },
];

export const alerts: AlertItem[] = [
  {
    id: '1',
    message: 'Module temperature exceeding defined limits',
    type: 'warning',
    timestamp: '2025-05-15T10:23:45',
  },
  {
    id: '2',
    message: 'Potential hotspot detected in cell B4',
    type: 'error',
    timestamp: '2025-05-15T10:18:32',
  },
  {
    id: '3',
    message: 'Test profile changed to STC',
    type: 'info',
    timestamp: '2025-05-15T10:15:00',
  },
  {
    id: '4',
    message: 'Humidity level slightly above optimal range',
    type: 'warning',
    timestamp: '2025-05-15T10:05:12',
  },
];

export const envParameters: EnvironmentalParameter[] = [
  {
    name: 'Temperature',
    value: 24.8,
    unit: '°C',
    min: 20,
    max: 30,
    status: 'normal',
  },
  {
    name: 'Humidity',
    value: 52.3,
    unit: '%',
    min: 40,
    max: 60,
    status: 'normal',
  },
  {
    name: 'Irradiance',
    value: 998.5,
    unit: 'W/m²',
    min: 950,
    max: 1050,
    status: 'normal',
  },
  {
    name: 'Chamber Pressure',
    value: 101.2,
    unit: 'kPa',
    min: 100,
    max: 102,
    status: 'normal',
  },
];

// Generate IV curve data points
export const generateIVCurveData = (): IVDataPoint[] => {
  const data: IVDataPoint[] = [];
  // Model IV curve for a typical solar module
  for (let v = 0; v <= 50; v += 0.5) {
    // Simplified IV curve model
    let i = 10; // Short circuit current
    if (v > 40) {
      i = 10 * Math.max(0, 1 - (v - 40) / 10); // Linear drop after 40V
    } else if (v > 30) {
      i = 10 * Math.max(0, 1 - 0.05 * (v - 30) ** 2); // Knee curve
    }
    data.push({ voltage: v, current: i });
  }
  return data;
};

export const ivCurveData: IVDataPoint[] = generateIVCurveData();

export const temperatureHistory = Array.from({ length: 20 }, (_, i) => ({
  time: new Date(Date.now() - (19 - i) * 15 * 60 * 1000).toISOString(),
  value: 25 + 2 * Math.sin(i / 3) + Math.random() * 0.5,
}));

export const humidityHistory = Array.from({ length: 20 }, (_, i) => ({
  time: new Date(Date.now() - (19 - i) * 15 * 60 * 1000).toISOString(),
  value: 50 + 5 * Math.sin(i / 4) + Math.random(),
}));

export const moduleImages: ModuleImage[] = [
  {
    id: '1',
    type: 'EL',
    url: 'https://images.pexels.com/photos/7267588/pexels-photo-7267588.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    timestamp: '2025-05-15T09:45:00',
    defects: [
      { x: 25, y: 15, severity: 'medium', type: 'Crack' },
      { x: 75, y: 60, severity: 'low', type: 'Contact Issue' },
    ],
  },
  {
    id: '2',
    type: 'IR',
    url: 'https://images.pexels.com/photos/414837/pexels-photo-414837.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    timestamp: '2025-05-15T09:46:30',
    defects: [
      { x: 30, y: 40, severity: 'high', type: 'Hotspot' },
    ],
  },
  {
    id: '3',
    type: 'EL',
    url: 'https://images.pexels.com/photos/7267588/pexels-photo-7267588.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    timestamp: '2025-05-15T10:15:00',
    defects: [],
  },
  {
    id: '4',
    type: 'IR',
    url: 'https://images.pexels.com/photos/414837/pexels-photo-414837.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    timestamp: '2025-05-15T10:16:30',
    defects: [
      { x: 65, y: 25, severity: 'medium', type: 'Hotspot' },
    ],
  },
];