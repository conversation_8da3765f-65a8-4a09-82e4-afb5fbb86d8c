import React from 'react';
import { AlertItem } from '../types';
import { AlertTriangle, Info, XCircle, Bell } from 'lucide-react';

interface AlertsPanelProps {
  alerts: AlertItem[];
}

const AlertsPanel: React.FC<AlertsPanelProps> = ({ alerts }) => {
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <Info size={16} className="text-blue-500" />;
      case 'warning':
        return <AlertTriangle size={16} className="text-amber-500" />;
      case 'error':
        return <XCircle size={16} className="text-red-500" />;
      default:
        return <Info size={16} className="text-blue-500" />;
    }
  };
  
  const getAlertClass = (type: string) => {
    switch (type) {
      case 'info':
        return 'bg-blue-50 border-blue-200';
      case 'warning':
        return 'bg-amber-50 border-amber-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };
  
  const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  return (
    <div className="space-y-2 max-h-[350px] overflow-y-auto pr-1">
      {alerts.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <Bell className="mx-auto h-8 w-8 mb-2 text-gray-400" />
          <p>No alerts at this time</p>
        </div>
      ) : (
        alerts.map((alert) => (
          <div
            key={alert.id}
            className={`p-3 rounded-md border text-sm flex items-start ${getAlertClass(
              alert.type
            )}`}
          >
            <div className="mr-3 mt-0.5">{getAlertIcon(alert.type)}</div>
            <div className="flex-1">
              <div className="font-medium">{alert.message}</div>
              <div className="text-xs text-gray-500 mt-0.5">
                {formatTime(alert.timestamp)}
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default AlertsPanel;