import React, { useState } from 'react';
import { TestProfile } from '../types';
import Button from './Button';
import { Play, Pause, Square, Download, Save, BarChart3 } from 'lucide-react';

interface TestControlsProps {
  profiles: TestProfile[];
}

const TestControls: React.FC<TestControlsProps> = ({ profiles }) => {
  const [selectedProfile, setSelectedProfile] = useState(profiles[0].id);
  const [testStatus, setTestStatus] = useState<'idle' | 'running' | 'paused'>('idle');
  const [progress, setProgress] = useState(0);
  
  const handleStart = () => {
    setTestStatus('running');
    // Simulate progress
    let currentProgress = 0;
    const interval = setInterval(() => {
      currentProgress += 5;
      setProgress(currentProgress);
      if (currentProgress >= 100) {
        clearInterval(interval);
        setTestStatus('idle');
      }
    }, 1000);
  };
  
  const handlePause = () => {
    setTestStatus('paused');
  };
  
  const handleStop = () => {
    setTestStatus('idle');
    setProgress(0);
  };
  
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Test Profile
        </label>
        <select
          value={selectedProfile}
          onChange={(e) => setSelectedProfile(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          disabled={testStatus !== 'idle'}
        >
          {profiles.map((profile) => (
            <option key={profile.id} value={profile.id}>
              {profile.name}
            </option>
          ))}
        </select>
        <p className="mt-1 text-sm text-gray-500">
          {profiles.find((p) => p.id === selectedProfile)?.description}
        </p>
      </div>
      
      {testStatus !== 'idle' && (
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>Progress</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      )}
      
      <div className="flex space-x-3">
        {testStatus === 'idle' && (
          <Button
            variant="primary"
            icon={<Play size={18} />}
            onClick={handleStart}
            className="flex-1"
          >
            Start Test
          </Button>
        )}
        
        {testStatus === 'running' && (
          <>
            <Button
              variant="warning"
              icon={<Pause size={18} />}
              onClick={handlePause}
              className="flex-1"
            >
              Pause
            </Button>
            <Button
              variant="danger"
              icon={<Square size={18} />}
              onClick={handleStop}
            >
              Stop
            </Button>
          </>
        )}
        
        {testStatus === 'paused' && (
          <>
            <Button
              variant="primary"
              icon={<Play size={18} />}
              onClick={() => setTestStatus('running')}
              className="flex-1"
            >
              Resume
            </Button>
            <Button
              variant="danger"
              icon={<Square size={18} />}
              onClick={handleStop}
            >
              Stop
            </Button>
          </>
        )}
      </div>
      
      <div className="pt-2 border-t border-gray-200">
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            icon={<Save size={16} />}
            disabled={testStatus !== 'idle'}
          >
            Save Profile
          </Button>
          <Button
            variant="secondary"
            size="sm"
            icon={<Download size={16} />}
          >
            Export Data
          </Button>
          <Button
            variant="secondary"
            size="sm"
            icon={<BarChart3 size={16} />}
          >
            Generate Report
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TestControls;