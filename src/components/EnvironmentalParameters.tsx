import React from 'react';
import { EnvironmentalParameter } from '../types';
import StatusIndicator from './StatusIndicator';
import EnvironmentalChart from './charts/EnvironmentalChart';

interface EnvironmentalParametersProps {
  parameters: EnvironmentalParameter[];
  temperatureHistory: { time: string; value: number }[];
  humidityHistory: { time: string; value: number }[];
}

const EnvironmentalParameters: React.FC<EnvironmentalParametersProps> = ({
  parameters,
  temperatureHistory,
  humidityHistory,
}) => {
  const getParameterColor = (parameter: string) => {
    switch (parameter.toLowerCase()) {
      case 'temperature':
        return '#ef4444';
      case 'humidity':
        return '#0ea5e9';
      case 'irradiance':
        return '#f59e0b';
      case 'chamber pressure':
        return '#8b5cf6';
      default:
        return '#3b82f6';
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        {parameters.map((param) => (
          <div key={param.name} className="bg-white rounded-lg border border-gray-200 p-3">
            <div className="flex justify-between items-center mb-2">
              <div className="font-medium text-sm">{param.name}</div>
              <StatusIndicator status={param.status} />
            </div>
            
            <div className="flex justify-between items-baseline">
              <div className="text-2xl font-semibold">
                {param.value.toFixed(1)}
                <span className="text-sm text-gray-500 ml-1">{param.unit}</span>
              </div>
              <div className="text-xs text-gray-500">
                Range: {param.min}-{param.max} {param.unit}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-3">
          <EnvironmentalChart
            data={temperatureHistory}
            parameter="Temperature"
            unit="°C"
            color="#ef4444"
            minValue={20}
            maxValue={30}
          />
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-3">
          <EnvironmentalChart
            data={humidityHistory}
            parameter="Humidity"
            unit="%"
            color="#0ea5e9"
            minValue={40}
            maxValue={60}
          />
        </div>
      </div>
    </div>
  );
};

export default EnvironmentalParameters;