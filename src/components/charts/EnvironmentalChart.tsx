import React from 'react';

interface DataPoint {
  time: string;
  value: number;
}

interface EnvironmentalChartProps {
  data: DataPoint[];
  parameter: string;
  unit: string;
  color?: string;
  height?: number;
  minValue?: number;
  maxValue?: number;
}

const EnvironmentalChart: React.FC<EnvironmentalChartProps> = ({
  data,
  parameter,
  unit,
  color = '#3b82f6',
  height = 100,
  minValue,
  maxValue,
}) => {
  // Calculate min and max for scaling if not provided
  const calculatedMin = minValue ?? Math.min(...data.map(d => d.value)) - 1;
  const calculatedMax = maxValue ?? Math.max(...data.map(d => d.value)) + 1;
  
  // Scale points to fit the chart
  const scaleX = (index: number) => (index / (data.length - 1)) * 100;
  const scaleY = (value: number) => 
    100 - ((value - calculatedMin) / (calculatedMax - calculatedMin)) * 100;
  
  // Create path for the line
  const linePath = data
    .map((point, i) => {
      const x = scaleX(i);
      const y = scaleY(point.value);
      return `${i === 0 ? 'M' : 'L'}${x},${y}`;
    })
    .join(' ');

  // Format time label
  const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  return (
    <div>
      <div className="flex justify-between items-center mb-1">
        <span className="text-xs font-medium text-gray-700">{parameter}</span>
        <span className="text-xs text-gray-500">
          Current: {data[data.length - 1]?.value.toFixed(1)} {unit}
        </span>
      </div>
      
      <svg
        width="100%"
        height={height}
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        className="overflow-visible"
      >
        {/* Background with gradient */}
        <defs>
          <linearGradient id={`gradient-${parameter}`} x1="0" x2="0" y1="0" y2="1">
            <stop offset="0%" stopColor={color} stopOpacity="0.2" />
            <stop offset="100%" stopColor={color} stopOpacity="0.05" />
          </linearGradient>
        </defs>
        
        {/* Fill area under the line */}
        <path
          d={`${linePath} L100,100 L0,100 Z`}
          fill={`url(#gradient-${parameter})`}
        />
        
        {/* Line */}
        <path
          d={linePath}
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Data points */}
        {data.map((point, i) => (
          <circle
            key={i}
            cx={scaleX(i)}
            cy={scaleY(point.value)}
            r="2"
            fill="white"
            stroke={color}
            strokeWidth="1"
          />
        ))}
      </svg>
      
      {/* Time labels */}
      <div className="flex justify-between mt-1">
        <span className="text-xs text-gray-500">{formatTime(data[0]?.time)}</span>
        <span className="text-xs text-gray-500">{formatTime(data[data.length - 1]?.time)}</span>
      </div>
    </div>
  );
};

export default EnvironmentalChart;