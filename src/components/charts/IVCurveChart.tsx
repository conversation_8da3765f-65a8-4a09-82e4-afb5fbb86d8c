import React, { useState } from 'react';
import { IVDataPoint } from '../../types';
import { ZoomIn, ZoomOut, RefreshCw } from 'lucide-react';
import Button from '../Button';

interface IVCurveChartProps {
  data: IVDataPoint[];
  height?: number;
  width?: string;
}

const IVCurveChart: React.FC<IVCurveChartProps> = ({
  data,
  height = 300,
  width = '100%',
}) => {
  const [zoom, setZoom] = useState(1);
  const [hoverPoint, setHoverPoint] = useState<IVDataPoint | null>(null);

  // Find max values for scaling
  const maxVoltage = Math.max(...data.map(d => d.voltage));
  const maxCurrent = Math.max(...data.map(d => d.current));

  // Apply zoom factor
  const effectiveMaxVoltage = maxVoltage / zoom;
  const effectiveMaxCurrent = maxCurrent / zoom;

  // Calculate power points
  const powerData = data.map(d => ({
    voltage: d.voltage,
    power: d.voltage * d.current,
  }));
  const maxPower = Math.max(...powerData.map(d => d.power));

  // Scale data points to fit the chart
  const scaleX = (voltage: number) => (voltage / effectiveMaxVoltage) * 100;
  const scaleY = (current: number) => 100 - (current / effectiveMaxCurrent) * 100;
  const scalePower = (power: number) => 100 - (power / maxPower) * 100;

  // Create path for the IV curve
  const ivPath = data
    .map((point, i) => {
      const x = scaleX(point.voltage);
      const y = scaleY(point.current);
      return `${i === 0 ? 'M' : 'L'}${x},${y}`;
    })
    .join(' ');

  // Create path for the power curve
  const powerPath = powerData
    .map((point, i) => {
      const x = scaleX(point.voltage);
      const y = scalePower(point.power);
      return `${i === 0 ? 'M' : 'L'}${x},${y}`;
    })
    .join(' ');

  // Handle mouse movement for hover effects
  const handleMouseMove = (e: React.MouseEvent<SVGSVGElement>) => {
    const svg = e.currentTarget;
    const rect = svg.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Convert to chart coordinates
    const chartX = (x / rect.width) * effectiveMaxVoltage;
    const chartY = effectiveMaxCurrent - (y / rect.height) * effectiveMaxCurrent;
    
    // Find the closest point
    let closestPoint = data[0];
    let minDistance = Number.MAX_VALUE;
    
    data.forEach(point => {
      const distance = Math.sqrt(
        Math.pow(scaleX(point.voltage) - (x / rect.width) * 100, 2) + 
        Math.pow(scaleY(point.current) - (y / rect.height) * 100, 2)
      );
      
      if (distance < minDistance) {
        minDistance = distance;
        closestPoint = point;
      }
    });
    
    setHoverPoint(closestPoint);
  };

  const handleMouseLeave = () => {
    setHoverPoint(null);
  };

  return (
    <div className="relative">
      <div className="absolute right-0 top-0 flex space-x-2">
        <Button 
          variant="secondary" 
          size="sm" 
          icon={<ZoomIn size={16} />}
          onClick={() => setZoom(prev => Math.min(prev + 0.5, 3))}
        >
          Zoom In
        </Button>
        <Button 
          variant="secondary" 
          size="sm" 
          icon={<ZoomOut size={16} />}
          onClick={() => setZoom(prev => Math.max(prev - 0.5, 1))}
        >
          Zoom Out
        </Button>
        <Button 
          variant="secondary" 
          size="sm" 
          icon={<RefreshCw size={16} />}
          onClick={() => setZoom(1)}
        >
          Reset
        </Button>
      </div>
      
      <div className="mt-12 relative">
        <svg
          width={width}
          height={height}
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
          className="border border-gray-300 bg-white rounded-md"
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
        >
          {/* Grid lines */}
          {[0, 25, 50, 75, 100].map(percent => (
            <React.Fragment key={`grid-${percent}`}>
              <line
                x1="0"
                y1={percent}
                x2="100"
                y2={percent}
                stroke="#e5e7eb"
                strokeWidth="0.5"
              />
              <line
                x1={percent}
                y1="0"
                x2={percent}
                y2="100"
                stroke="#e5e7eb"
                strokeWidth="0.5"
              />
            </React.Fragment>
          ))}

          {/* IV Curve */}
          <path d={ivPath} fill="none" stroke="#3b82f6" strokeWidth="2" />
          
          {/* Power Curve */}
          <path d={powerPath} fill="none" stroke="#10b981" strokeWidth="2" strokeDasharray="3,2" />
          
          {/* Hover point */}
          {hoverPoint && (
            <circle
              cx={scaleX(hoverPoint.voltage)}
              cy={scaleY(hoverPoint.current)}
              r="3"
              fill="#3b82f6"
              stroke="white"
              strokeWidth="1.5"
            />
          )}
        </svg>
        
        {/* Axis labels */}
        <div className="flex justify-between mt-1 px-2 text-xs text-gray-600">
          <span>0V</span>
          <span>{(effectiveMaxVoltage / 4).toFixed(1)}V</span>
          <span>{(effectiveMaxVoltage / 2).toFixed(1)}V</span>
          <span>{(3 * effectiveMaxVoltage / 4).toFixed(1)}V</span>
          <span>{effectiveMaxVoltage.toFixed(1)}V</span>
        </div>
        
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-1 text-xs text-gray-600">
          <span>{effectiveMaxCurrent.toFixed(1)}A</span>
          <span>{(3 * effectiveMaxCurrent / 4).toFixed(1)}A</span>
          <span>{(effectiveMaxCurrent / 2).toFixed(1)}A</span>
          <span>{(effectiveMaxCurrent / 4).toFixed(1)}A</span>
          <span>0A</span>
        </div>
      </div>
      
      {/* Legend */}
      <div className="flex justify-center mt-4 text-sm">
        <div className="flex items-center mr-4">
          <div className="w-4 h-1 bg-blue-500 mr-1"></div>
          <span>I-V Curve</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-1 bg-green-500 mr-1 border-t border-b border-dashed"></div>
          <span>Power Curve</span>
        </div>
      </div>
      
      {/* Hover data */}
      {hoverPoint && (
        <div className="absolute bottom-0 right-0 bg-gray-800 text-white p-2 rounded text-xs">
          <div>Voltage: {hoverPoint.voltage.toFixed(2)} V</div>
          <div>Current: {hoverPoint.current.toFixed(2)} A</div>
          <div>Power: {(hoverPoint.voltage * hoverPoint.current).toFixed(2)} W</div>
        </div>
      )}
    </div>
  );
};

export default IVCurveChart;