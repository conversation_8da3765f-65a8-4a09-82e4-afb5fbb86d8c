import React from 'react';

interface StatusIndicatorProps {
  status: 'normal' | 'warning' | 'critical';
  size?: 'sm' | 'md' | 'lg';
  pulse?: boolean;
  label?: string;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'md',
  pulse = false,
  label,
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'normal':
        return 'bg-green-500';
      case 'warning':
        return 'bg-amber-500';
      case 'critical':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'w-2 h-2';
      case 'md':
        return 'w-3 h-3';
      case 'lg':
        return 'w-4 h-4';
      default:
        return 'w-3 h-3';
    }
  };

  return (
    <div className="flex items-center">
      <div className={`relative rounded-full ${getSizeClass()} ${getStatusColor()}`}>
        {pulse && (
          <span
            className={`absolute inset-0 rounded-full ${getStatusColor()} opacity-75 animate-ping`}
          ></span>
        )}
      </div>
      {label && <span className="ml-2 text-sm text-gray-700">{label}</span>}
    </div>
  );
};

export default StatusIndicator;