import React from 'react';
import { NavItem } from '../types';
import { 
  LayoutDashboard, 
  Settings, 
  FileBarChart, 
  LineChart, 
  FlaskConical, 
  ChevronRight,
  SunMedium
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const navItems: NavItem[] = [
    { title: 'Dashboard', icon: 'LayoutDashboard', path: '/', active: true },
    { title: 'Test Setup', icon: 'FlaskConical', path: '/setup' },
    { title: 'Real-Time Monitoring', icon: 'LineChart', path: '/monitoring' },
    { title: 'Reports', icon: 'FileBarChart', path: '/reports' },
    { title: 'Settings', icon: 'Settings', path: '/settings' },
  ];

  const getIcon = (icon: string) => {
    switch (icon) {
      case 'LayoutDashboard':
        return <LayoutDashboard size={20} />;
      case 'FlaskConical':
        return <FlaskConical size={20} />;
      case 'LineChart':
        return <LineChart size={20} />;
      case 'FileBarChart':
        return <FileBarChart size={20} />;
      case 'Settings':
        return <Settings size={20} />;
      default:
        return <ChevronRight size={20} />;
    }
  };

  return (
    <div className="w-64 bg-gray-800 text-white h-full flex flex-col shadow-lg">
      <div className="p-4 flex items-center border-b border-gray-700">
        <SunMedium className="h-8 w-8 text-yellow-400 mr-2" />
        <div>
          <h1 className="text-xl font-bold">SolarTest Pro</h1>
          <p className="text-xs text-gray-400">PV Module Testing</p>
        </div>
      </div>
      <nav className="flex-1 px-2 py-4 space-y-1">
        {navItems.map((item, index) => (
          <a
            key={index}
            href={item.path}
            className={`flex items-center px-4 py-3 rounded-md transition-colors duration-200 ${
              item.active
                ? 'bg-blue-700 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`}
          >
            <span className="mr-3">{getIcon(item.icon)}</span>
            <span>{item.title}</span>
          </a>
        ))}
      </nav>
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold">
            ST
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium">SolarTech Labs</p>
            <p className="text-xs text-gray-400">Admin</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;