import React, { useState } from 'react';
import { ModuleImage } from '../types';
import Card from './Card';
import Badge from './Badge';
import { ExternalLink, ZoomIn } from 'lucide-react';

interface ImageGalleryProps {
  images: ModuleImage[];
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ images }) => {
  const [selectedImage, setSelectedImage] = useState<ModuleImage | null>(null);
  
  const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };
  
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'border-yellow-400 bg-yellow-100';
      case 'medium':
        return 'border-orange-400 bg-orange-100';
      case 'high':
        return 'border-red-500 bg-red-100';
      default:
        return 'border-gray-400 bg-gray-100';
    }
  };
  
  return (
    <div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {images.map((image) => (
          <div 
            key={image.id} 
            className="relative cursor-pointer group"
            onClick={() => setSelectedImage(image)}
          >
            <div className="aspect-square overflow-hidden rounded-lg border border-gray-200 relative">
              <img
                src={image.url}
                alt={`${image.type} Image`}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300 flex items-center justify-center">
                <ZoomIn className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
              
              {/* Defect markers */}
              {image.defects.map((defect, index) => (
                <div
                  key={index}
                  className={`absolute w-6 h-6 -ml-3 -mt-3 rounded-full border-2 ${getSeverityColor(
                    defect.severity
                  )} flex items-center justify-center text-xs font-bold`}
                  style={{
                    left: `${defect.x}%`,
                    top: `${defect.y}%`,
                  }}
                >
                  {index + 1}
                </div>
              ))}
              
              <Badge
                variant={image.type === 'EL' ? 'info' : 'warning'}
                className="absolute top-2 right-2"
              >
                {image.type}
              </Badge>
            </div>
            <div className="mt-1 text-xs text-gray-500">
              {formatTime(image.timestamp)}
              {image.defects.length > 0 && (
                <span className="ml-2 text-red-500">
                  {image.defects.length} {image.defects.length === 1 ? 'defect' : 'defects'}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {/* Modal for selected image */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={() => setSelectedImage(null)}>
          <div className="bg-white rounded-lg max-w-3xl w-full max-h-[90vh] overflow-auto" onClick={(e) => e.stopPropagation()}>
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="font-medium">
                {selectedImage.type} Image - {formatTime(selectedImage.timestamp)}
              </h3>
              <button onClick={() => setSelectedImage(null)} className="text-gray-500 hover:text-gray-700">
                &times;
              </button>
            </div>
            <div className="p-4">
              <div className="relative">
                <img
                  src={selectedImage.url}
                  alt={`${selectedImage.type} Image`}
                  className="w-full rounded-lg"
                />
                
                {/* Defect markers on modal */}
                {selectedImage.defects.map((defect, index) => (
                  <div
                    key={index}
                    className={`absolute w-8 h-8 -ml-4 -mt-4 rounded-full border-2 ${getSeverityColor(
                      defect.severity
                    )} flex items-center justify-center text-sm font-bold`}
                    style={{
                      left: `${defect.x}%`,
                      top: `${defect.y}%`,
                    }}
                  >
                    {index + 1}
                  </div>
                ))}
              </div>
              
              {selectedImage.defects.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-medium mb-2">Detected Defects:</h4>
                  <div className="space-y-2">
                    {selectedImage.defects.map((defect, index) => (
                      <div key={index} className="flex items-center">
                        <div className={`w-6 h-6 rounded-full mr-2 flex items-center justify-center ${getSeverityColor(defect.severity)}`}>
                          {index + 1}
                        </div>
                        <div>
                          <span className="font-medium">{defect.type}</span>
                          <span className="ml-2 text-sm text-gray-500">
                            Severity: {defect.severity}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="mt-4 flex justify-end">
                <button
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <ExternalLink size={16} className="mr-1" />
                  Open in Analysis Tool
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGallery;