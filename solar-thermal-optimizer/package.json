{"name": "solarpro", "private": true, "version": "1.0.0", "type": "module", "description": "Comprehensive solar project management platform for planning, design, and optimization", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "axios": "^1.6.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "plotly.js": "^2.27.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.20.0", "date-fns": "^2.30.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/plotly.js": "^2.12.29", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}