import React from 'react';
import { Map, Layers, Grid, Ruler, Settings, Download } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

/**
 * Layout Page Component
 * 
 * Site layout design and planning interface providing:
 * - Interactive site map
 * - Panel placement optimization
 * - Shading analysis
 * - Layout configuration tools
 */
const Layout: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Site Layout</h1>
          <p className="text-gray-600 mt-1">
            Design and optimize solar panel placement for maximum efficiency
          </p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <Download className="h-4 w-4 mr-2" />
            Export Layout
          </button>
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </button>
        </div>
      </div>

      {/* Layout Tools */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Layout Canvas */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Map className="h-5 w-5 mr-2" />
                Interactive Layout Designer
              </CardTitle>
              <div className="flex space-x-2">
                <Badge variant="secondary">Auto-Optimize</Badge>
                <Badge variant="secondary">Shading Analysis</Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="relative h-96 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
              {/* Placeholder for interactive layout canvas */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <Map className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Layout Canvas</h3>
                  <p className="text-gray-500">Interactive site layout designer will be displayed here</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Features: Drag & drop panels, auto-optimization, shading analysis
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Layout Tools Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Layers className="h-5 w-5 mr-2" />
              Layout Tools
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50">
                <div className="flex items-center">
                  <Grid className="h-5 w-5 text-blue-500 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Auto Layout</p>
                    <p className="text-sm text-gray-500">Optimize panel placement</p>
                  </div>
                </div>
              </button>
              
              <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50">
                <div className="flex items-center">
                  <Ruler className="h-5 w-5 text-green-500 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Measurement</p>
                    <p className="text-sm text-gray-500">Distance & area tools</p>
                  </div>
                </div>
              </button>
              
              <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50">
                <div className="flex items-center">
                  <Layers className="h-5 w-5 text-purple-500 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Layers</p>
                    <p className="text-sm text-gray-500">Toggle map layers</p>
                  </div>
                </div>
              </button>
            </div>

            {/* Layout Stats */}
            <div className="pt-4 border-t">
              <h4 className="font-medium text-gray-900 mb-3">Layout Statistics</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Panels:</span>
                  <span className="font-medium">540</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Capacity:</span>
                  <span className="font-medium">1.2 MW</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Site Utilization:</span>
                  <span className="font-medium">78%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shading Loss:</span>
                  <span className="font-medium text-orange-600">3.2%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Layout Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Panel Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Panel Type
                </label>
                <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                  <option>Monocrystalline 540W</option>
                  <option>Polycrystalline 450W</option>
                  <option>Thin Film 380W</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tilt Angle
                </label>
                <input 
                  type="range" 
                  min="0" 
                  max="60" 
                  defaultValue="30"
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>0°</span>
                  <span>30°</span>
                  <span>60°</span>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Row Spacing (m)
                </label>
                <input 
                  type="number" 
                  defaultValue="4.5"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Optimization Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">Recommended Layout</h4>
                <div className="text-sm text-green-700 space-y-1">
                  <p>• Optimal tilt: 32°</p>
                  <p>• Row spacing: 4.2m</p>
                  <p>• Expected yield: 1,847 MWh/year</p>
                  <p>• Performance ratio: 84.2%</p>
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Alternative Layout</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>• Higher density option</p>
                  <p>• +15% more panels</p>
                  <p>• -8% yield per panel</p>
                  <p>• Net gain: +5.8% total yield</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Layout;
