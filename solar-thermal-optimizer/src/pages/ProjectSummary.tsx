import React from 'react';
import { 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  MapPin,
  Package,
  Users,
  Calendar,
  Zap,
  DollarSign
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

/**
 * SolarPro Project Summary Dashboard
 * 
 * Comprehensive project management dashboard providing:
 * - Project overview and timeline
 * - Interactive site map with installation zones
 * - Supply chain and material tracking
 * - Team and milestone management
 */
const ProjectSummary: React.FC = () => {
  const projectStats = [
    {
      title: '1.2 MW Capacity',
      subtitle: 'Team: 8 members',
      icon: <Zap className="h-5 w-5" />,
      color: 'blue'
    },
    {
      title: '42% Complete',
      subtitle: 'On Schedule',
      icon: <TrendingUp className="h-5 w-5" />,
      color: 'green'
    },
    {
      title: '5 days',
      subtitle: 'Next Milestone',
      icon: <Clock className="h-5 w-5" />,
      color: 'orange'
    }
  ];

  const supplyChainItems = [
    {
      id: 1,
      material: 'Solar Panels (540W)',
      supplier: 'SunTech Inc.',
      quantity: 540,
      leadTime: '12 weeks',
      expectedDelivery: 'Jul 12, 2025',
      status: 'On Track'
    },
    {
      id: 2,
      material: 'Central Inverters (250kW)',
      supplier: 'PowerConvert Ltd.',
      quantity: 5,
      leadTime: '16 weeks',
      expectedDelivery: 'Aug 3, 2025',
      status: 'Delayed by 14 days'
    },
    {
      id: 3,
      material: 'Mounting Structure',
      supplier: 'SteelFrame Co.',
      quantity: 1,
      leadTime: '8 weeks',
      expectedDelivery: 'Jun 15, 2025',
      status: 'On Track'
    }
  ];

  const getStatusBadge = (status: string) => {
    if (status.includes('Delayed')) {
      return <Badge variant="destructive">Delayed</Badge>;
    }
    return <Badge variant="success">On Track</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">SunValley Commercial Campus</h1>
          <div className="flex items-center space-x-4 mt-2">
            <span className="text-gray-600">Start: Apr 12, 2025</span>
            <span className="text-gray-600">End: Dec 30, 2025</span>
            <Badge variant="warning">42% Complete</Badge>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-500">
            Last updated: {new Date().toLocaleTimeString()}
          </span>
        </div>
      </div>

      {/* Project Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {projectStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stat.title}</p>
                  <p className="text-sm text-gray-600">{stat.subtitle}</p>
                </div>
                <div className={`h-12 w-12 bg-${stat.color}-50 rounded-lg flex items-center justify-center`}>
                  {stat.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Site Map */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                Site Layout
              </CardTitle>
              <div className="flex space-x-2">
                <Badge variant="secondary">Leaflet</Badge>
                <Badge variant="secondary">OpenStreetMap</Badge>
                <Badge variant="secondary">CARTO</Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="relative h-80 bg-gray-900 rounded-lg overflow-hidden">
              {/* Simulated Map Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900"></div>
              
              {/* Installation Zones */}
              <div className="absolute top-16 left-16 w-32 h-24 bg-yellow-500 bg-opacity-30 border-2 border-yellow-400 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full mx-auto mb-1"></div>
                  <span className="text-xs text-yellow-200 font-medium">Zone A</span>
                </div>
              </div>
              
              <div className="absolute top-32 right-20 w-28 h-32 bg-blue-500 bg-opacity-30 border-2 border-blue-400 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-3 h-3 bg-blue-400 rounded-full mx-auto mb-1"></div>
                  <span className="text-xs text-blue-200 font-medium">Zone B</span>
                </div>
              </div>
              
              <div className="absolute bottom-16 left-1/3 w-36 h-20 bg-green-500 bg-opacity-30 border-2 border-green-400 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-3 h-3 bg-red-400 rounded-full mx-auto mb-1"></div>
                  <span className="text-xs text-green-200 font-medium">Zone C</span>
                </div>
              </div>
              
              {/* Connection Lines */}
              <svg className="absolute inset-0 w-full h-full">
                <defs>
                  <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#374151" strokeWidth="0.5" opacity="0.3"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                <path d="M 80 80 Q 200 120 280 160" stroke="#60A5FA" strokeWidth="2" fill="none" strokeDasharray="5,5" opacity="0.7"/>
                <path d="M 280 160 Q 320 200 240 240" stroke="#60A5FA" strokeWidth="2" fill="none" strokeDasharray="5,5" opacity="0.7"/>
              </svg>
            </div>
            
            {/* Map Legend */}
            <div className="mt-4 flex items-center justify-between text-sm">
              <div className="flex space-x-4">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                  <span>Installation Complete</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
                  <span>In Progress</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                  <span>Issue Detected</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Milestone */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
              Next Milestone
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">5 days</div>
              <p className="text-sm text-gray-600">Inverter Delivery</p>
              <p className="text-xs text-gray-500 mt-1">Critical component for Phase 1 completion</p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Progress</span>
                <span className="font-medium">Risk Level: High</span>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center text-xs text-gray-600">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Order Placed
                </div>
                <div className="flex items-center text-xs text-gray-600">
                  <Clock className="h-3 w-3 mr-2 text-yellow-500" />
                  Manufacturing
                </div>
                <div className="flex items-center text-xs text-gray-400">
                  <div className="h-3 w-3 mr-2 border border-gray-300 rounded-full"></div>
                  Estimated completion
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Supply Chain Timeline */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Supply Chain Timeline
            </CardTitle>
            <div className="flex space-x-2">
              <Badge variant="secondary">Material Lead Times</Badge>
              <Badge variant="secondary">Cost Estimates</Badge>
              <Badge variant="secondary">AI Recommendations</Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-6 gap-4 text-sm font-medium text-gray-600 border-b pb-2">
              <div>Material</div>
              <div>Quantity</div>
              <div>Lead Time</div>
              <div>Expected Delivery</div>
              <div>Status</div>
              <div></div>
            </div>

            {supplyChainItems.map((item) => (
              <div key={item.id} className="grid grid-cols-6 gap-4 text-sm py-3 border-b border-gray-100">
                <div>
                  <div className="font-medium text-gray-900">{item.material}</div>
                  <div className="text-gray-500">{item.supplier}</div>
                </div>
                <div className="text-gray-900">{item.quantity}</div>
                <div className="text-gray-900">{item.leadTime}</div>
                <div className="text-gray-900">{item.expectedDelivery}</div>
                <div>{getStatusBadge(item.status)}</div>
                <div className="text-right">
                  <button className="text-blue-600 hover:text-blue-800 text-sm">View Details</button>
                </div>
              </div>
            ))}
          </div>

          {/* Timeline Progress */}
          <div className="mt-6 pt-4 border-t">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-gray-600">Order Placed</span>
              <span className="text-gray-600">Manufacturing</span>
              <span className="text-gray-600">Estimated completion</span>
            </div>
            <div className="relative">
              <div className="absolute left-0 top-1/2 w-full h-0.5 bg-gray-200"></div>
              <div className="absolute left-0 top-1/2 w-1/3 h-0.5 bg-green-500"></div>
              <div className="flex justify-between">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectSummary;
