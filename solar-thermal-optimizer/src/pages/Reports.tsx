import React from 'react';
import { FileText, Download, Calendar, BarChart3, <PERSON><PERSON><PERSON>U<PERSON>, <PERSON><PERSON><PERSON>, Share2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

/**
 * Reports Page Component
 * 
 * Comprehensive reporting and analytics interface providing:
 * - Project performance reports
 * - Financial analysis
 * - Progress tracking
 * - Custom report generation
 */
const Reports: React.FC = () => {
  const reportTemplates = [
    {
      id: 1,
      name: 'Project Status Report',
      description: 'Comprehensive project overview with timeline and milestones',
      category: 'Project Management',
      frequency: 'Weekly',
      lastGenerated: '2025-01-15',
      format: 'PDF',
      recipients: 8
    },
    {
      id: 2,
      name: 'Financial Performance',
      description: 'Cost analysis, budget tracking, and financial projections',
      category: 'Financial',
      frequency: 'Monthly',
      lastGenerated: '2025-01-01',
      format: 'Excel',
      recipients: 5
    },
    {
      id: 3,
      name: 'Risk Assessment Summary',
      description: 'Risk register, mitigation status, and impact analysis',
      category: 'Risk Management',
      frequency: 'Bi-weekly',
      lastGenerated: '2025-01-10',
      format: 'PDF',
      recipients: 12
    },
    {
      id: 4,
      name: 'Supply Chain Status',
      description: 'Material delivery tracking and procurement updates',
      category: 'Procurement',
      frequency: 'Weekly',
      lastGenerated: '2025-01-14',
      format: 'PDF',
      recipients: 6
    }
  ];

  const quickStats = [
    {
      title: 'Reports Generated',
      value: '47',
      change: '+12',
      period: 'this month',
      icon: <FileText className="h-5 w-5" />,
      color: 'blue'
    },
    {
      title: 'Active Subscriptions',
      value: '23',
      change: '+3',
      period: 'new this week',
      icon: <Share2 className="h-5 w-5" />,
      color: 'green'
    },
    {
      title: 'Data Sources',
      value: '8',
      change: '+1',
      period: 'integrated',
      icon: <BarChart3 className="h-5 w-5" />,
      color: 'purple'
    }
  ];

  const recentReports = [
    {
      name: 'Weekly Status Report - Week 3',
      type: 'Project Status',
      generated: '2025-01-15 14:30',
      size: '2.4 MB',
      downloads: 15
    },
    {
      name: 'Risk Assessment - January 2025',
      type: 'Risk Management',
      generated: '2025-01-14 09:15',
      size: '1.8 MB',
      downloads: 8
    },
    {
      name: 'Supply Chain Update - Week 3',
      type: 'Procurement',
      generated: '2025-01-14 16:45',
      size: '1.2 MB',
      downloads: 12
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600 mt-1">
            Generate insights and track project performance
          </p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <FileText className="h-4 w-4 mr-2" />
            New Report
          </button>
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {quickStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`h-12 w-12 bg-${stat.color}-50 rounded-lg flex items-center justify-center`}>
                  {stat.icon}
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span className="text-sm font-medium text-green-600">{stat.change}</span>
                <span className="text-sm text-gray-500 ml-2">{stat.period}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Report Templates */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Report Templates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportTemplates.map((template) => (
                <div key={template.id} className="p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{template.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                    </div>
                    <Badge variant="secondary">{template.category}</Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                    <div>
                      <span className="text-gray-600">Frequency:</span>
                      <span className="ml-2 font-medium">{template.frequency}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Format:</span>
                      <span className="ml-2 font-medium">{template.format}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Last Generated:</span>
                      <span className="ml-2 font-medium">{template.lastGenerated}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Recipients:</span>
                      <span className="ml-2 font-medium">{template.recipients}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button className="flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                      <FileText className="h-3 w-3 mr-1" />
                      Generate
                    </button>
                    <button className="flex items-center px-3 py-1 border border-gray-300 text-sm rounded hover:bg-gray-50">
                      <Calendar className="h-3 w-3 mr-1" />
                      Schedule
                    </button>
                    <button className="flex items-center px-3 py-1 border border-gray-300 text-sm rounded hover:bg-gray-50">
                      Edit
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="h-5 w-5 mr-2" />
              Recent Reports
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentReports.map((report, index) => (
              <div key={index} className="p-3 border rounded-lg">
                <h4 className="font-medium text-gray-900 mb-1">{report.name}</h4>
                <p className="text-sm text-gray-600 mb-2">{report.type}</p>
                <div className="text-xs text-gray-500 space-y-1">
                  <div>Generated: {report.generated}</div>
                  <div>Size: {report.size}</div>
                  <div>Downloads: {report.downloads}</div>
                </div>
                <button className="mt-2 flex items-center text-blue-600 hover:text-blue-800 text-sm">
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </button>
              </div>
            ))}
            
            <button className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-500 hover:text-blue-600 text-sm">
              View All Reports
            </button>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Dashboard */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Project Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Performance Dashboard</h3>
                <p className="text-gray-500">Interactive charts and KPI tracking</p>
                <p className="text-sm text-gray-400 mt-2">
                  Timeline progress, budget utilization, risk trends
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2" />
              Cost Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Budget Summary</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <div className="flex justify-between">
                    <span>Total Budget:</span>
                    <span className="font-medium">$1,250,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Spent to Date:</span>
                    <span className="font-medium">$525,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Remaining:</span>
                    <span className="font-medium">$725,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Variance:</span>
                    <span className="font-medium text-green-700">-3.2%</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">Cost Categories</h4>
                <div className="text-sm text-green-700 space-y-1">
                  <div className="flex justify-between">
                    <span>Materials:</span>
                    <span className="font-medium">68%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Labor:</span>
                    <span className="font-medium">22%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Equipment:</span>
                    <span className="font-medium">7%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Other:</span>
                    <span className="font-medium">3%</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Custom Report Builder */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Report Builder</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Report Type
              </label>
              <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                <option>Project Summary</option>
                <option>Financial Analysis</option>
                <option>Risk Assessment</option>
                <option>Performance Metrics</option>
                <option>Custom Dashboard</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date Range
              </label>
              <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                <option>Last 7 days</option>
                <option>Last 30 days</option>
                <option>Last 3 months</option>
                <option>Year to date</option>
                <option>Custom range</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Output Format
              </label>
              <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                <option>PDF Report</option>
                <option>Excel Spreadsheet</option>
                <option>PowerPoint Presentation</option>
                <option>Interactive Dashboard</option>
              </select>
            </div>
          </div>
          
          <div className="mt-6 flex items-center space-x-4">
            <button className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </button>
            <button className="flex items-center px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Recurring
            </button>
            <button className="flex items-center px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
              Preview
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Reports;
