import React from 'react';
import { AlertTriangle, Shield, TrendingDown, Clock, DollarSign, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

/**
 * Risk Assessment Page Component
 * 
 * Risk management interface providing:
 * - Project risk identification and assessment
 * - Mitigation strategies
 * - Risk monitoring and tracking
 * - Impact analysis
 */
const Risk: React.FC = () => {
  const riskCategories = [
    {
      category: 'Technical',
      risks: 12,
      high: 2,
      medium: 6,
      low: 4,
      color: 'red'
    },
    {
      category: 'Financial',
      risks: 8,
      high: 1,
      medium: 3,
      low: 4,
      color: 'orange'
    },
    {
      category: 'Environmental',
      risks: 6,
      high: 0,
      medium: 2,
      low: 4,
      color: 'green'
    },
    {
      category: 'Regulatory',
      risks: 4,
      high: 1,
      medium: 1,
      low: 2,
      color: 'blue'
    }
  ];

  const topRisks = [
    {
      id: 1,
      title: 'Inverter Delivery Delay',
      category: 'Supply Chain',
      probability: 'High',
      impact: 'High',
      severity: 'Critical',
      mitigation: 'Alternative supplier identified',
      owner: '<PERSON>',
      dueDate: '2025-04-15'
    },
    {
      id: 2,
      title: 'Weather-Related Installation Delays',
      category: 'Environmental',
      probability: 'Medium',
      impact: 'Medium',
      severity: 'Medium',
      mitigation: 'Flexible scheduling implemented',
      owner: 'Sarah Johnson',
      dueDate: '2025-05-01'
    },
    {
      id: 3,
      title: 'Grid Connection Approval',
      category: 'Regulatory',
      probability: 'Low',
      impact: 'High',
      severity: 'High',
      mitigation: 'Early engagement with utility',
      owner: 'Mike Davis',
      dueDate: '2025-06-30'
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Risk Assessment</h1>
          <p className="text-gray-600 mt-1">
            Identify, assess, and mitigate project risks
          </p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Add Risk
          </button>
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Shield className="h-4 w-4 mr-2" />
            Risk Register
          </button>
        </div>
      </div>

      {/* Risk Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {riskCategories.map((category, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium text-gray-900">{category.category}</h3>
                <div className={`h-3 w-3 bg-${category.color}-500 rounded-full`}></div>
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-2">{category.risks}</div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-red-600">High:</span>
                  <span className="font-medium">{category.high}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-orange-600">Medium:</span>
                  <span className="font-medium">{category.medium}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600">Low:</span>
                  <span className="font-medium">{category.low}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top Risks */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
              Top Risks Requiring Attention
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topRisks.map((risk) => (
                <div key={risk.id} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{risk.title}</h4>
                      <p className="text-sm text-gray-600">{risk.category}</p>
                    </div>
                    <Badge className={getSeverityColor(risk.severity)}>
                      {risk.severity}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                    <div>
                      <span className="text-gray-600">Probability:</span>
                      <span className="ml-2 font-medium">{risk.probability}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Impact:</span>
                      <span className="ml-2 font-medium">{risk.impact}</span>
                    </div>
                  </div>
                  
                  <div className="text-sm mb-3">
                    <span className="text-gray-600">Mitigation:</span>
                    <span className="ml-2">{risk.mitigation}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Owner: {risk.owner}</span>
                    <span className="text-gray-600">Due: {risk.dueDate}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Risk Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingDown className="h-5 w-5 mr-2" />
              Risk Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-red-700">Critical Risks</span>
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </div>
              <div className="text-2xl font-bold text-red-900">4</div>
              <div className="text-sm text-red-600">Require immediate attention</div>
            </div>
            
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-yellow-700">Overdue Actions</span>
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-yellow-900">2</div>
              <div className="text-sm text-yellow-600">Past due date</div>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-700">Financial Impact</span>
                <DollarSign className="h-4 w-4 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-blue-900">$127K</div>
              <div className="text-sm text-blue-600">Potential cost exposure</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-green-700">Mitigated Risks</span>
                <Shield className="h-4 w-4 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-900">18</div>
              <div className="text-sm text-green-600">Successfully addressed</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Risk Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Risk Assessment Matrix</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-6 gap-2 text-center text-sm">
            {/* Header */}
            <div></div>
            <div className="font-medium">Very Low</div>
            <div className="font-medium">Low</div>
            <div className="font-medium">Medium</div>
            <div className="font-medium">High</div>
            <div className="font-medium">Very High</div>
            
            {/* Probability rows */}
            <div className="font-medium">Very High</div>
            <div className="h-12 bg-yellow-200 border rounded flex items-center justify-center">2</div>
            <div className="h-12 bg-orange-200 border rounded flex items-center justify-center">3</div>
            <div className="h-12 bg-red-200 border rounded flex items-center justify-center">1</div>
            <div className="h-12 bg-red-300 border rounded flex items-center justify-center">2</div>
            <div className="h-12 bg-red-400 border rounded flex items-center justify-center">0</div>
            
            <div className="font-medium">High</div>
            <div className="h-12 bg-green-200 border rounded flex items-center justify-center">4</div>
            <div className="h-12 bg-yellow-200 border rounded flex items-center justify-center">5</div>
            <div className="h-12 bg-orange-200 border rounded flex items-center justify-center">3</div>
            <div className="h-12 bg-red-200 border rounded flex items-center justify-center">2</div>
            <div className="h-12 bg-red-300 border rounded flex items-center justify-center">1</div>
            
            <div className="font-medium">Medium</div>
            <div className="h-12 bg-green-200 border rounded flex items-center justify-center">6</div>
            <div className="h-12 bg-green-200 border rounded flex items-center justify-center">4</div>
            <div className="h-12 bg-yellow-200 border rounded flex items-center justify-center">2</div>
            <div className="h-12 bg-orange-200 border rounded flex items-center justify-center">1</div>
            <div className="h-12 bg-red-200 border rounded flex items-center justify-center">0</div>
            
            <div className="font-medium">Low</div>
            <div className="h-12 bg-green-100 border rounded flex items-center justify-center">3</div>
            <div className="h-12 bg-green-200 border rounded flex items-center justify-center">2</div>
            <div className="h-12 bg-green-200 border rounded flex items-center justify-center">1</div>
            <div className="h-12 bg-yellow-200 border rounded flex items-center justify-center">0</div>
            <div className="h-12 bg-orange-200 border rounded flex items-center justify-center">0</div>
            
            <div className="font-medium">Very Low</div>
            <div className="h-12 bg-green-100 border rounded flex items-center justify-center">2</div>
            <div className="h-12 bg-green-100 border rounded flex items-center justify-center">1</div>
            <div className="h-12 bg-green-200 border rounded flex items-center justify-center">0</div>
            <div className="h-12 bg-green-200 border rounded flex items-center justify-center">0</div>
            <div className="h-12 bg-yellow-200 border rounded flex items-center justify-center">0</div>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Impact →</strong> (columns) | <strong>Probability ↓</strong> (rows)</p>
            <p>Numbers represent count of risks in each category</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Risk;
