import React from 'react';
import { Package, Download, Search, Filter, DollarSign, Truck, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

/**
 * Bill of Materials (BoM) Page Component
 * 
 * Comprehensive material management interface providing:
 * - Complete bill of materials
 * - Procurement tracking
 * - Cost analysis
 * - Supplier management
 */
const BoM: React.FC = () => {
  const bomItems = [
    {
      id: 1,
      category: 'Solar Panels',
      item: 'Monocrystalline Solar Panel 540W',
      partNumber: 'SP-540-MONO-72',
      quantity: 540,
      unit: 'pcs',
      unitCost: 285.50,
      totalCost: 154170,
      supplier: 'SunTech Inc.',
      leadTime: '12 weeks',
      status: 'Ordered',
      delivery: '2025-07-12'
    },
    {
      id: 2,
      category: 'Inverters',
      item: 'Central Inverter 250kW',
      partNumber: 'INV-250-CENTRAL',
      quantity: 5,
      unit: 'pcs',
      unitCost: 18500.00,
      totalCost: 92500,
      supplier: 'PowerConvert Ltd.',
      leadTime: '16 weeks',
      status: 'Delayed',
      delivery: '2025-08-17'
    },
    {
      id: 3,
      category: 'Mounting',
      item: 'Aluminum Mounting Rail 4m',
      partNumber: 'MR-AL-4000',
      quantity: 270,
      unit: 'pcs',
      unitCost: 125.00,
      totalCost: 33750,
      supplier: 'SteelFrame Co.',
      leadTime: '8 weeks',
      status: 'In Transit',
      delivery: '2025-06-15'
    },
    {
      id: 4,
      category: 'Electrical',
      item: 'DC Combiner Box 12-string',
      partNumber: 'DCB-12S-600V',
      quantity: 45,
      unit: 'pcs',
      unitCost: 450.00,
      totalCost: 20250,
      supplier: 'ElectroSafe Corp.',
      leadTime: '6 weeks',
      status: 'Delivered',
      delivery: '2025-05-28'
    },
    {
      id: 5,
      category: 'Cables',
      item: 'DC Cable 4mm² XLPE',
      partNumber: 'DC-4MM-XLPE',
      quantity: 2500,
      unit: 'm',
      unitCost: 3.25,
      totalCost: 8125,
      supplier: 'CableTech Solutions',
      leadTime: '4 weeks',
      status: 'Delivered',
      delivery: '2025-05-15'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Delivered':
        return <Badge variant="success">Delivered</Badge>;
      case 'In Transit':
        return <Badge variant="warning">In Transit</Badge>;
      case 'Ordered':
        return <Badge variant="secondary">Ordered</Badge>;
      case 'Delayed':
        return <Badge variant="destructive">Delayed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const totalProjectCost = bomItems.reduce((sum, item) => sum + item.totalCost, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Bill of Materials</h1>
          <p className="text-gray-600 mt-1">
            Complete material list and procurement tracking
          </p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <Download className="h-4 w-4 mr-2" />
            Export BoM
          </button>
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Package className="h-4 w-4 mr-2" />
            Add Item
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Cost</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${totalProjectCost.toLocaleString()}
                </p>
              </div>
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-green-600">Within Budget</span>
              <span className="text-sm text-gray-500 ml-2">-3.2% under</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Items</p>
                <p className="text-2xl font-bold text-gray-900">{bomItems.length}</p>
              </div>
              <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-blue-600">Categories</span>
              <span className="text-sm text-gray-500 ml-2">5 different</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Delivered</p>
                <p className="text-2xl font-bold text-gray-900">
                  {bomItems.filter(item => item.status === 'Delivered').length}
                </p>
              </div>
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <Truck className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-green-600">On Schedule</span>
              <span className="text-sm text-gray-500 ml-2">40% complete</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Delayed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {bomItems.filter(item => item.status === 'Delayed').length}
                </p>
              </div>
              <div className="h-12 w-12 bg-red-50 rounded-lg flex items-center justify-center">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-red-600">Critical</span>
              <span className="text-sm text-gray-500 ml-2">Needs attention</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search materials..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select className="border border-gray-300 rounded-lg px-3 py-2">
              <option>All Categories</option>
              <option>Solar Panels</option>
              <option>Inverters</option>
              <option>Mounting</option>
              <option>Electrical</option>
              <option>Cables</option>
            </select>
            <select className="border border-gray-300 rounded-lg px-3 py-2">
              <option>All Status</option>
              <option>Delivered</option>
              <option>In Transit</option>
              <option>Ordered</option>
              <option>Delayed</option>
            </select>
            <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
          </div>
        </CardContent>
      </Card>

      {/* BoM Table */}
      <Card>
        <CardHeader>
          <CardTitle>Material List</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Item</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Part Number</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-600">Quantity</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-600">Unit Cost</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-600">Total Cost</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Supplier</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Delivery</th>
                </tr>
              </thead>
              <tbody>
                {bomItems.map((item) => (
                  <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{item.item}</div>
                        <div className="text-sm text-gray-500">{item.category}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-gray-600 font-mono text-sm">
                      {item.partNumber}
                    </td>
                    <td className="py-4 px-4 text-right">
                      <span className="font-medium">{item.quantity.toLocaleString()}</span>
                      <span className="text-gray-500 ml-1">{item.unit}</span>
                    </td>
                    <td className="py-4 px-4 text-right font-medium">
                      ${item.unitCost.toFixed(2)}
                    </td>
                    <td className="py-4 px-4 text-right font-medium">
                      ${item.totalCost.toLocaleString()}
                    </td>
                    <td className="py-4 px-4 text-gray-600">
                      {item.supplier}
                    </td>
                    <td className="py-4 px-4">
                      {getStatusBadge(item.status)}
                    </td>
                    <td className="py-4 px-4 text-gray-600">
                      {item.delivery}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Cost Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Cost Breakdown by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {['Solar Panels', 'Inverters', 'Mounting', 'Electrical', 'Cables'].map((category) => {
                const categoryItems = bomItems.filter(item => item.category === category);
                const categoryTotal = categoryItems.reduce((sum, item) => sum + item.totalCost, 0);
                const percentage = (categoryTotal / totalProjectCost * 100).toFixed(1);
                
                return (
                  <div key={category} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                      <span className="text-gray-700">{category}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">${categoryTotal.toLocaleString()}</div>
                      <div className="text-sm text-gray-500">{percentage}%</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Procurement Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                Key delivery milestones and dependencies
              </div>
              
              {bomItems
                .sort((a, b) => new Date(a.delivery).getTime() - new Date(b.delivery).getTime())
                .map((item) => (
                  <div key={item.id} className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      item.status === 'Delivered' ? 'bg-green-500' :
                      item.status === 'In Transit' ? 'bg-yellow-500' :
                      item.status === 'Delayed' ? 'bg-red-500' : 'bg-gray-300'
                    }`}></div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{item.item}</div>
                      <div className="text-sm text-gray-500">{item.delivery}</div>
                    </div>
                    {getStatusBadge(item.status)}
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default BoM;
