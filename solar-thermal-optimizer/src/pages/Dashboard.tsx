import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  TrendingUp,
  AlertTriangle,
  DollarSign,
  Thermometer,
  ArrowRight,
  Calendar,
  MapPin,
  Activity,
  RefreshCw,
  DownloadCloud
} from 'lucide-react';
import Card from '../components/Common/Card';
import Button from '../components/Common/Button';
import LoadingSpinner from '../components/Common/LoadingSpinner';
import { useAppContext } from '../contexts/AppContext';
import { SummaryStats, ThermalDefect, DefectCategory, DefectSeverity } from '../types';
import { mockDefects, mockProjects } from '../services/mockData';

/**
 * Dashboard Page Component
 * 
 * Main overview page showing:
 * - Key metrics and statistics
 * - Recent defects and alerts
 * - Active projects status
 * - Quick action buttons
 */
const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { selectedSite, selectedFlight, loading } = useAppContext();
  const [stats, setStats] = useState<SummaryStats | null>(null);
  const [recentDefects, setRecentDefects] = useState<ThermalDefect[]>([]);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      if (!selectedSite) return;

      try {
        // In a real app, this would call API endpoints
        // For now, calculate stats from mock data
        const siteDefects = mockDefects.filter(d => d.siteId === selectedSite.id);
        
        const calculatedStats: SummaryStats = {
          totalDefects: siteDefects.length,
          criticalDefects: siteDefects.filter(d => d.severity === 'critical').length,
          totalPowerLoss: siteDefects.reduce((sum, d) => sum + d.impact.powerLoss, 0),
          averageTemperature: siteDefects.reduce((sum, d) => sum + d.temperature.average, 0) / siteDefects.length || 0,
          estimatedRepairCost: siteDefects.reduce((sum, d) => sum + d.repairCost.estimated, 0),
          defectsByCategory: siteDefects.reduce((acc, d) => {
            acc[d.category] = (acc[d.category] || 0) + 1;
            return acc;
          }, {} as Record<DefectCategory, number>),
          defectsBySeverity: siteDefects.reduce((acc, d) => {
            acc[d.severity] = (acc[d.severity] || 0) + 1;
            return acc;
          }, {} as Record<DefectSeverity, number>)
        };

        setStats(calculatedStats);
        setRecentDefects(siteDefects.slice(0, 5)); // Show 5 most recent
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      }
    };

    loadDashboardData();
  }, [selectedSite]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading.isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  if (!selectedSite) {
    return (
      <div className="text-center py-12">
        <MapPin className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Site Selected</h3>
        <p className="text-gray-500">Please select a solar site to view dashboard data.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Solar Thermal Defect Analysis</h1>
        <div className="text-sm text-gray-500">Last updated: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}</div>
      </div>

      {/* Main Grid Layout */}
      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2">
          {/* Key Metrics */}
          {stats && (
            <Card
              title="Defect Analysis Overview"
              headerRight={
                <div className="flex items-center space-x-2">
                  <button className="text-gray-500 hover:text-gray-700">
                    <RefreshCw size={14} />
                  </button>
                  <button className="text-gray-500 hover:text-gray-700">
                    <DownloadCloud size={14} />
                  </button>
                </div>
              }
            >
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Activity className="h-8 w-8 text-blue-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-blue-600">Total Defects</p>
                      <p className="text-2xl font-bold text-blue-900">{stats.totalDefects}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <AlertTriangle className="h-8 w-8 text-red-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-red-600">Critical Defects</p>
                      <p className="text-2xl font-bold text-red-900">{stats.criticalDefects}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <TrendingUp className="h-8 w-8 text-green-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-green-600">Power Loss</p>
                      <p className="text-2xl font-bold text-green-900">
                        {stats.totalPowerLoss.toFixed(1)} kW
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <DollarSign className="h-8 w-8 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-orange-600">Repair Cost</p>
                      <p className="text-2xl font-bold text-orange-900">
                        ${stats.estimatedRepairCost.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          {/* Quick Actions */}
          <Card title="Optimization Controls">
            <div className="space-y-3">
              <Button
                variant="primary"
                className="w-full justify-start"
                onClick={() => navigate('/defects')}
                icon={<Activity size={16} />}
              >
                Run Defect Analysis
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => navigate('/projects')}
                icon={<Calendar size={16} />}
              >
                Create New Project
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => navigate('/defect-management')}
                icon={<AlertTriangle size={16} />}
              >
                Manage Defects
              </Button>
            </div>
          </Card>

          {/* Active Projects Summary */}
          <Card
            title="Alerts & Warnings"
            headerRight={
              <div className="flex items-center space-x-2">
                <button className="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                  View All <ArrowRight size={14} />
                </button>
              </div>
            }
          >
            <div className="space-y-3">
              {mockProjects.filter(p => p.status === 'in_progress').map((project) => (
                <div key={project.id} className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-900 mb-1">
                    {project.name}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{project.progress}% complete</span>
                    <span className="text-green-600">${project.budget.remaining.toLocaleString()} remaining</span>
                  </div>
                  <div className="mt-2 bg-gray-200 rounded-full h-1.5">
                    <div
                      className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>

      {/* Recent Defects Table */}
      <div>
        <Card
          title="Recent Thermal Defects"
          headerRight={
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">{recentDefects.length} defects total</span>
              <button className="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                View All <ArrowRight size={14} />
              </button>
            </div>
          }
        >
          {recentDefects.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <AlertTriangle className="mx-auto h-8 w-8 mb-2 text-gray-400" />
              <p>No defects found for this site</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentDefects.map((defect) => (
                <div
                  key={defect.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onClick={() => navigate(`/defect-management/${defect.id}`)}
                >
                  <div className="flex items-center space-x-3">
                    <Thermometer className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {defect.category.replace('_', ' ').toUpperCase()} - {defect.location.panel}
                      </p>
                      <p className="text-xs text-gray-500">
                        {defect.temperature.max.toFixed(1)}°C • {defect.impact.powerLoss.toFixed(1)} kW loss
                      </p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(defect.severity)}`}>
                    {defect.severity}
                  </span>
                </div>
              ))}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
