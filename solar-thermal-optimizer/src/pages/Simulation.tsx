import React from 'react';
import { Zap, Sun, Cloud, TrendingUp, BarChart3, Play, Settings } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

/**
 * Simulation Page Component
 * 
 * Performance modeling and simulation interface providing:
 * - Energy yield predictions
 * - Weather impact analysis
 * - Performance optimization scenarios
 * - Financial modeling
 */
const Simulation: React.FC = () => {
  const simulationScenarios = [
    {
      name: 'Base Case',
      description: 'Current design with standard conditions',
      yield: '1,847 MWh/year',
      performance: '84.2%',
      status: 'completed'
    },
    {
      name: 'High Efficiency',
      description: 'Premium panels with optimized layout',
      yield: '2,156 MWh/year',
      performance: '89.1%',
      status: 'running'
    },
    {
      name: 'Cost Optimized',
      description: 'Budget panels with maximum density',
      yield: '1,723 MWh/year',
      performance: '81.7%',
      status: 'pending'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success">Completed</Badge>;
      case 'running':
        return <Badge variant="warning">Running</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Performance Simulation</h1>
          <p className="text-gray-600 mt-1">
            Model energy yield and optimize system performance
          </p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Play className="h-4 w-4 mr-2" />
            Run Simulation
          </button>
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Settings className="h-4 w-4 mr-2" />
            Parameters
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Annual Yield</p>
                <p className="text-2xl font-bold text-gray-900">1,847 MWh</p>
              </div>
              <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <Zap className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-green-600">+12.3%</span>
              <span className="text-sm text-gray-500 ml-2">vs. initial estimate</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Performance Ratio</p>
                <p className="text-2xl font-bold text-gray-900">84.2%</p>
              </div>
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-green-600">Excellent</span>
              <span className="text-sm text-gray-500 ml-2">industry benchmark</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Solar Irradiance</p>
                <p className="text-2xl font-bold text-gray-900">1,654 kWh/m²</p>
              </div>
              <div className="h-12 w-12 bg-yellow-50 rounded-lg flex items-center justify-center">
                <Sun className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-yellow-600">High</span>
              <span className="text-sm text-gray-500 ml-2">annual average</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Weather Impact</p>
                <p className="text-2xl font-bold text-gray-900">-3.1%</p>
              </div>
              <div className="h-12 w-12 bg-gray-50 rounded-lg flex items-center justify-center">
                <Cloud className="h-6 w-6 text-gray-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-gray-600">Low</span>
              <span className="text-sm text-gray-500 ml-2">shading losses</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Simulation Results */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Monthly Energy Production
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80 bg-gray-50 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Energy Production Chart</h3>
                <p className="text-gray-500">Monthly energy yield visualization</p>
                <p className="text-sm text-gray-400 mt-2">
                  Interactive chart showing seasonal variations and weather impacts
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Simulation Scenarios */}
        <Card>
          <CardHeader>
            <CardTitle>Simulation Scenarios</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {simulationScenarios.map((scenario, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{scenario.name}</h4>
                  {getStatusBadge(scenario.status)}
                </div>
                <p className="text-sm text-gray-600 mb-3">{scenario.description}</p>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Annual Yield:</span>
                    <span className="font-medium">{scenario.yield}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Performance:</span>
                    <span className="font-medium">{scenario.performance}</span>
                  </div>
                </div>
              </div>
            ))}
            
            <button className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-500 hover:text-blue-600">
              + Add New Scenario
            </button>
          </CardContent>
        </Card>
      </div>

      {/* Simulation Parameters */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Environmental Parameters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Weather Data Source
                </label>
                <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                  <option>TMY3 - Typical Meteorological Year</option>
                  <option>PVGIS - Photovoltaic GIS</option>
                  <option>NREL NSRDB - National Solar Database</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Simulation Period
                </label>
                <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                  <option>25 Years (Full Lifecycle)</option>
                  <option>1 Year (Annual Analysis)</option>
                  <option>Custom Period</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Degradation Rate (%/year)
                </label>
                <input 
                  type="number" 
                  step="0.1"
                  defaultValue="0.5"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Financial Parameters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Electricity Price ($/kWh)
                </label>
                <input 
                  type="number" 
                  step="0.01"
                  defaultValue="0.12"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Discount Rate (%)
                </label>
                <input 
                  type="number" 
                  step="0.1"
                  defaultValue="6.0"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Financial Projections</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>• Annual Revenue: $221,640</p>
                  <p>• 25-year NPV: $3.2M</p>
                  <p>• Payback Period: 7.2 years</p>
                  <p>• IRR: 12.8%</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Simulation;
