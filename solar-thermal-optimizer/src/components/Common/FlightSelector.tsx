import React, { useState, useEffect } from 'react';
import { ChevronDown, Calendar, Plane, CheckCircle, Clock, XCircle } from 'lucide-react';
import { useAppContext } from '../../contexts/AppContext';
import { FlightData } from '../../types';
import { mockFlights } from '../../services/mockData';
import { format } from 'date-fns';

/**
 * Flight Selector Component
 * 
 * Dropdown component for selecting flight data for the currently selected site.
 * Shows flight dates, status, and weather conditions.
 */
const FlightSelector: React.FC = () => {
  const { selectedSite, selectedFlight, setSelectedFlight, setLoading } = useAppContext();
  const [flights, setFlights] = useState<FlightData[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  // Load flights when selected site changes
  useEffect(() => {
    const loadFlights = async () => {
      if (!selectedSite) {
        setFlights([]);
        return;
      }

      setLoading({ isLoading: true });
      try {
        // In a real app, this would call siteService.getFlights(selectedSite.id)
        // For now, filter mock data by site
        const siteFlights = mockFlights.filter(flight => flight.siteId === selectedSite.id);
        setFlights(siteFlights);
        
        // Auto-select the most recent completed flight
        const completedFlights = siteFlights.filter(f => f.status === 'completed');
        if (completedFlights.length > 0 && !selectedFlight) {
          const mostRecent = completedFlights.sort((a, b) => 
            new Date(b.date).getTime() - new Date(a.date).getTime()
          )[0];
          setSelectedFlight(mostRecent);
        }
      } catch (error) {
        setLoading({ 
          isLoading: false, 
          error: 'Failed to load flight data' 
        });
      } finally {
        setLoading({ isLoading: false });
      }
    };

    loadFlights();
  }, [selectedSite, selectedFlight, setSelectedFlight, setLoading]);

  const handleFlightSelect = (flight: FlightData) => {
    setSelectedFlight(flight);
    setIsOpen(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'processing':
        return <Clock size={14} className="text-yellow-600" />;
      case 'failed':
        return <XCircle size={14} className="text-red-600" />;
      default:
        return <Clock size={14} className="text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'processing':
        return 'text-yellow-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return dateString;
    }
  };

  if (!selectedSite) {
    return null;
  }

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        Flight Date
      </label>
      
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full md:w-64 bg-white border border-gray-300 rounded-md px-3 py-2 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
      >
        <div className="flex items-center justify-between">
          {selectedFlight ? (
            <div className="flex items-center space-x-2">
              {getStatusIcon(selectedFlight.status)}
              <span className="text-sm font-medium text-gray-900">
                {formatDate(selectedFlight.date)}
              </span>
            </div>
          ) : (
            <span className="text-sm text-gray-500">Select flight date...</span>
          )}
          <ChevronDown 
            size={16} 
            className={`text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          />
        </div>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full md:w-80 bg-white border border-gray-200 rounded-md shadow-lg">
          <div className="max-h-60 overflow-auto py-1">
            {flights.length === 0 ? (
              <div className="px-3 py-4 text-center text-sm text-gray-500">
                No flight data available for this site
              </div>
            ) : (
              flights.map((flight) => (
                <button
                  key={flight.id}
                  onClick={() => handleFlightSelect(flight)}
                  className="w-full px-3 py-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusIcon(flight.status)}
                        <span className="text-sm font-medium text-gray-900">
                          {formatDate(flight.date)}
                        </span>
                        <span className={`text-xs capitalize ${getStatusColor(flight.status)}`}>
                          {flight.status}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Plane size={12} />
                          <span>{flight.pilot}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar size={12} />
                          <span>{flight.weather.conditions}</span>
                        </div>
                      </div>
                      
                      <div className="mt-1 text-xs text-gray-400">
                        {flight.weather.temperature}°C • {flight.weather.windSpeed} km/h wind
                      </div>
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      )}

      {/* Overlay to close dropdown when clicking outside */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default FlightSelector;
