import React from 'react';

interface CardProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  className?: string;
  headerRight?: React.ReactNode;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

/**
 * Reusable Card Component
 * 
 * A flexible card component with optional header, subtitle, and custom content.
 * Used throughout the application for consistent styling.
 */
const Card: React.FC<CardProps> = ({ 
  title, 
  subtitle, 
  children, 
  className = '', 
  headerRight,
  padding = 'md'
}) => {
  const getPaddingClass = () => {
    switch (padding) {
      case 'none':
        return '';
      case 'sm':
        return 'p-3';
      case 'md':
        return 'p-4';
      case 'lg':
        return 'p-6';
      default:
        return 'p-4';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {(title || headerRight) && (
        <div className={`border-b border-gray-200 ${getPaddingClass()}`}>
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
              )}
            </div>
            {headerRight && (
              <div className="flex items-center space-x-2">
                {headerRight}
              </div>
            )}
          </div>
        </div>
      )}

      <div className={title || headerRight ? getPaddingClass() : getPaddingClass()}>
        {children}
      </div>
    </div>
  );
};

export default Card;
