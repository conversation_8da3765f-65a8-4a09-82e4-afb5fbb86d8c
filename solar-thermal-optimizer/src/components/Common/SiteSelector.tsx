import React, { useState, useEffect } from 'react';
import { ChevronDown, MapPin, Zap } from 'lucide-react';
import { useAppContext } from '../../contexts/AppContext';
import { SolarSite } from '../../types';
import { mockSites } from '../../services/mockData';

/**
 * Site Selector Component
 * 
 * Dropdown component for selecting solar sites.
 * Integrates with global app context to manage selected site state.
 */
const SiteSelector: React.FC = () => {
  const { selectedSite, setSelectedSite, setLoading } = useAppContext();
  const [sites, setSites] = useState<SolarSite[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  // Load sites on component mount
  useEffect(() => {
    const loadSites = async () => {
      setLoading({ isLoading: true });
      try {
        // In a real app, this would call siteService.getSites()
        // For now, using mock data
        setSites(mockSites);
        
        // Set first site as default if none selected
        if (!selectedSite && mockSites.length > 0) {
          setSelectedSite(mockSites[0]);
        }
      } catch (error) {
        setLoading({ 
          isLoading: false, 
          error: 'Failed to load sites' 
        });
      } finally {
        setLoading({ isLoading: false });
      }
    };

    loadSites();
  }, [selectedSite, setSelectedSite, setLoading]);

  const handleSiteSelect = (site: SolarSite) => {
    setSelectedSite(site);
    setIsOpen(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600';
      case 'maintenance':
        return 'text-yellow-600';
      case 'offline':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusDot = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'maintenance':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        Solar Site
      </label>

      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full md:w-64 bg-white border border-gray-300 rounded-md px-3 py-2 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
      >
        <div className="flex items-center justify-between">
          {selectedSite ? (
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${getStatusDot(selectedSite.status)}`} />
              <span className="text-sm font-medium text-gray-900 truncate">
                {selectedSite.name}
              </span>
            </div>
          ) : (
            <span className="text-sm text-gray-500">Select a site...</span>
          )}
          <ChevronDown 
            size={16} 
            className={`text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          />
        </div>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full md:w-80 bg-white border border-gray-200 rounded-md shadow-lg">
          <div className="max-h-60 overflow-auto py-1">
            {sites.map((site) => (
              <button
                key={site.id}
                onClick={() => handleSiteSelect(site)}
                className="w-full px-3 py-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <div className={`w-2 h-2 rounded-full ${getStatusDot(site.status)}`} />
                      <span className="text-sm font-medium text-gray-900 truncate">
                        {site.name}
                      </span>
                      <span className={`text-xs capitalize ${getStatusColor(site.status)}`}>
                        {site.status}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <MapPin size={12} />
                        <span>{site.location.address.split(',')[1]?.trim() || 'Unknown'}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Zap size={12} />
                        <span>{site.capacity} MW</span>
                      </div>
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Overlay to close dropdown when clicking outside */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default SiteSelector;
