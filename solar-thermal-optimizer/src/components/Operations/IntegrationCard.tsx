import React from 'react';
import { Badge } from '../ui/Badge';
import { Card, CardContent } from '../ui/Card';

interface IntegrationCardProps {
  name: string;
  icon: React.ReactNode;
  status: 'Connected' | 'Not Connected';
  description: string;
  lastSync: string;
  color: string;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onConfigure?: () => void;
}

/**
 * Integration Card Component
 * 
 * Displays integration status and provides connection controls
 * for external services like Gmail, Slack, Google Sheets, etc.
 */
const IntegrationCard: React.FC<IntegrationCardProps> = ({
  name,
  icon,
  status,
  description,
  lastSync,
  color,
  onConnect,
  onDisconnect,
  onConfigure
}) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            <div className={`h-12 w-12 bg-${color}-50 rounded-lg flex items-center justify-center mr-4`}>
              {icon}
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{name}</h3>
              <p className="text-sm text-gray-600 mt-1">{description}</p>
            </div>
          </div>
          <Badge 
            variant={status === 'Connected' ? 'success' : 'secondary'}
          >
            {status}
          </Badge>
        </div>
        
        <div className="mt-4 flex items-center justify-between">
          <span className="text-sm text-gray-500">
            Last sync: {lastSync}
          </span>
          <div className="flex space-x-2">
            {status === 'Connected' ? (
              <>
                <button 
                  onClick={onConfigure}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Configure
                </button>
                <button 
                  onClick={onDisconnect}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Disconnect
                </button>
              </>
            ) : (
              <button 
                onClick={onConnect}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                Connect
              </button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default IntegrationCard;
