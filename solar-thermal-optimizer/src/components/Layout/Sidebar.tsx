import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Map,
  Zap,
  AlertTriangle,
  Package,
  FileText,
  Settings,
  Sun,
  X,
  TrendingUp,
  Clock
} from 'lucide-react';
import { NavItem } from '../../types';

interface SidebarProps {
  onClose?: () => void;
}

/**
 * Sidebar Navigation Component
 *
 * Provides main navigation for the application with:
 * - Project summary and overview
 * - Site layout design
 * - Performance simulation
 * - Risk assessment
 * - Bill of materials
 * - Reports and analytics
 */
const Sidebar: React.FC<SidebarProps> = ({ onClose }) => {
  const location = useLocation();

  const navItems: NavItem[] = [
    {
      title: 'Project Summary',
      icon: 'LayoutDashboard',
      path: '/',
      active: location.pathname === '/'
    },
    {
      title: 'Layout',
      icon: 'Map',
      path: '/layout',
      active: location.pathname === '/layout'
    },
    {
      title: 'Simulation',
      icon: 'Zap',
      path: '/simulation',
      active: location.pathname === '/simulation'
    },
    {
      title: 'Risk',
      icon: 'AlertTriangle',
      path: '/risk',
      active: location.pathname === '/risk'
    },
    {
      title: 'BoM',
      icon: 'Package',
      path: '/bom',
      active: location.pathname === '/bom'
    },
    {
      title: 'Reports',
      icon: 'FileText',
      path: '/reports',
      active: location.pathname === '/reports'
    },
    {
      title: 'Operations',
      icon: 'Settings',
      path: '/operations',
      active: location.pathname.startsWith('/operations')
    },
  ];

  const getIcon = (iconName: string) => {
    const iconProps = { size: 20 };
    switch (iconName) {
      case 'LayoutDashboard':
        return <LayoutDashboard {...iconProps} />;
      case 'Map':
        return <Map {...iconProps} />;
      case 'Zap':
        return <Zap {...iconProps} />;
      case 'AlertTriangle':
        return <AlertTriangle {...iconProps} />;
      case 'Package':
        return <Package {...iconProps} />;
      case 'FileText':
        return <FileText {...iconProps} />;
      case 'Settings':
        return <Settings {...iconProps} />;
      default:
        return <LayoutDashboard {...iconProps} />;
    }
  };

  return (
    <div className="w-64 bg-gray-800 text-white h-full flex flex-col shadow-lg">
      {/* Header */}
      <div className="p-4 flex items-center justify-between border-b border-gray-700">
        <div className="flex items-center">
          <Sun className="h-8 w-8 text-yellow-400 mr-2" />
          <div>
            <h1 className="text-xl font-bold">SolarPro</h1>
            <p className="text-xs text-gray-400">Project: SunValley Commercial Campus</p>
          </div>
        </div>
        
        {/* Close button for mobile */}
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 rounded-md text-gray-400 hover:text-gray-600 lg:hidden"
          >
            <X size={20} />
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1">
        {navItems.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            onClick={onClose}
            className={`flex items-center px-4 py-3 rounded-md transition-colors duration-200 ${
              item.active
                ? 'bg-blue-700 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`}
          >
            <span className="mr-3">{getIcon(item.icon)}</span>
            <span>{item.title}</span>
            {item.badge && (
              <span className="ml-auto bg-red-100 text-red-600 text-xs px-2 py-0.5 rounded-full">
                {item.badge}
              </span>
            )}
          </Link>
        ))}
      </nav>

      {/* Project Timeline */}
      <div className="p-4 border-t border-gray-700">
        <div className="bg-gray-700 rounded-lg p-3">
          <h3 className="text-sm font-medium text-white mb-2">Project Timeline</h3>
          <div className="space-y-2">
            <div className="text-xs text-gray-300">
              <div className="flex justify-between">
                <span>Start: Apr 12, 2025</span>
                <span>End: Dec 30, 2025</span>
              </div>
              <div className="mt-1 text-yellow-400">42% Complete</div>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="flex items-center text-gray-300">
                <Clock size={12} className="mr-1 text-blue-400" />
                Next Milestone
              </span>
              <span className="font-medium text-blue-400">5 days</span>
            </div>
            <div className="text-xs text-gray-300">
              <span className="text-orange-400">⚠</span> Inverter Delivery
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        <div className="text-xs text-gray-400 text-center">
          <p>1.2 MW Capacity • 8 members</p>
          <p className="mt-1">© 2024 SolarPro</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
