import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Search, 
  FolderKanban, 
  Database, 
  Settings, 
  Sun,
  X,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';
import { NavItem } from '../../types';

interface SidebarProps {
  onClose?: () => void;
}

/**
 * Sidebar Navigation Component
 * 
 * Provides main navigation for the application with:
 * - Dashboard overview
 * - Defect analysis and optimization
 * - Project management
 * - Defect management
 * - Settings
 */
const Sidebar: React.FC<SidebarProps> = ({ onClose }) => {
  const location = useLocation();

  const navItems: NavItem[] = [
    { 
      title: 'Dashboard', 
      icon: 'LayoutDashboard', 
      path: '/',
      active: location.pathname === '/'
    },
    { 
      title: 'Defect Analysis', 
      icon: 'Search', 
      path: '/defects',
      active: location.pathname === '/defects'
    },
    { 
      title: 'Project Management', 
      icon: 'FolderKanban', 
      path: '/projects',
      active: location.pathname.startsWith('/projects')
    },
    { 
      title: 'Defect Management', 
      icon: 'Database', 
      path: '/defect-management',
      active: location.pathname.startsWith('/defect-management')
    },
    { 
      title: 'Settings', 
      icon: 'Settings', 
      path: '/settings',
      active: location.pathname === '/settings'
    },
  ];

  const getIcon = (iconName: string) => {
    const iconProps = { size: 20 };
    switch (iconName) {
      case 'LayoutDashboard':
        return <LayoutDashboard {...iconProps} />;
      case 'Search':
        return <Search {...iconProps} />;
      case 'FolderKanban':
        return <FolderKanban {...iconProps} />;
      case 'Database':
        return <Database {...iconProps} />;
      case 'Settings':
        return <Settings {...iconProps} />;
      default:
        return <LayoutDashboard {...iconProps} />;
    }
  };

  return (
    <div className="w-64 bg-gray-800 text-white h-full flex flex-col shadow-lg">
      {/* Header */}
      <div className="p-4 flex items-center border-b border-gray-700">
        <Sun className="h-8 w-8 text-yellow-400 mr-2" />
        <div>
          <h1 className="text-xl font-bold">ThermalOpt Pro</h1>
          <p className="text-xs text-gray-400">Defect Optimization</p>
        </div>
      </div>
        
        {/* Close button for mobile */}
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 rounded-md text-gray-400 hover:text-gray-600 lg:hidden"
          >
            <X size={20} />
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1">
        {navItems.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            onClick={onClose}
            className={`flex items-center px-4 py-3 rounded-md transition-colors duration-200 ${
              item.active
                ? 'bg-blue-700 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`}
          >
            <span className="mr-3">{getIcon(item.icon)}</span>
            <span>{item.title}</span>
            {item.badge && (
              <span className="ml-auto bg-red-100 text-red-600 text-xs px-2 py-0.5 rounded-full">
                {item.badge}
              </span>
            )}
          </Link>
        ))}
      </nav>

      {/* Quick Stats */}
      <div className="p-4 border-t border-gray-700">
        <div className="bg-gray-700 rounded-lg p-3">
          <h3 className="text-sm font-medium text-white mb-2">Quick Stats</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="flex items-center text-gray-300">
                <AlertTriangle size={12} className="mr-1 text-red-400" />
                Critical Defects
              </span>
              <span className="font-medium text-red-400">3</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="flex items-center text-gray-300">
                <TrendingUp size={12} className="mr-1 text-green-400" />
                Active Projects
              </span>
              <span className="font-medium text-green-400">2</span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        <div className="text-xs text-gray-400 text-center">
          <p>Version 1.0.0</p>
          <p className="mt-1">© 2024 ThermalOpt Pro</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
