import React from 'react';
import { <PERSON>u, <PERSON>, User, ChevronDown } from 'lucide-react';
import { useAppContext } from '../../contexts/AppContext';
import SiteSelector from '../Common/SiteSelector';
import FlightSelector from '../Common/FlightSelector';

interface HeaderProps {
  title: string;
  onMenuClick: () => void;
}

/**
 * Header Component
 * 
 * Provides the top navigation bar with:
 * - Mobile menu toggle
 * - Page title
 * - Site and flight selectors
 * - User menu and notifications
 */
const Header: React.FC<HeaderProps> = ({ title, onMenuClick }) => {
  const { selectedSite, selectedFlight } = useAppContext();

  return (
    <header className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          {/* Mobile menu button */}
          <button
            onClick={onMenuClick}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 lg:hidden"
          >
            <Menu size={20} />
          </button>

          {/* Page title */}
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
            {selectedSite && (
              <div className="text-sm text-gray-500">
                Last updated: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}
              </div>
            )}
          </div>
        </div>

        {/* Center section - Site and Flight selectors */}
        <div className="hidden md:flex items-center space-x-4">
          <SiteSelector />
          {selectedSite && <FlightSelector />}
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-3">
          {/* Notifications */}
          <button className="relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md">
            <Bell size={20} />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>

          {/* User menu */}
          <div className="relative">
            <button className="flex items-center space-x-2 p-2 text-gray-700 hover:bg-gray-100 rounded-md">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User size={16} />
              </div>
              <span className="hidden sm:block text-sm font-medium">John Doe</span>
              <ChevronDown size={16} className="hidden sm:block" />
            </button>
            
            {/* User dropdown menu - would be implemented with proper state management */}
            {/* <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
              <div className="py-1">
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                <div className="border-t border-gray-100"></div>
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
              </div>
            </div> */}
          </div>
        </div>
      </div>

      {/* Mobile site/flight selectors */}
      <div className="md:hidden mt-3 flex flex-col space-y-2">
        <SiteSelector />
        {selectedSite && <FlightSelector />}
      </div>
    </header>
  );
};

export default Header;
