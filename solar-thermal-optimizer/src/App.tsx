import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AppProvider } from './contexts/AppContext';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import ProjectSummary from './pages/ProjectSummary';
import SiteLayout from './pages/Layout';
import Simulation from './pages/Simulation';
import Risk from './pages/Risk';
import BoM from './pages/BoM';
import Reports from './pages/Reports';
import DefectAnalysis from './pages/DefectAnalysis';
import ProjectManagement from './pages/ProjectManagement';
import DefectManagement from './pages/DefectManagement';
import Settings from './pages/Settings';

/**
 * Main Application Component
 *
 * Provides routing and global context for the SolarPro application.
 * The app includes project management, site design, simulation, risk assessment, and reporting capabilities.
 */
function App() {
  return (
    <AppProvider>
      <Router>
        <Layout>
          <Routes>
            {/* Project Summary - Main dashboard */}
            <Route path="/" element={<ProjectSummary />} />

            {/* Layout - Site layout and design */}
            <Route path="/layout" element={<SiteLayout />} />

            {/* Simulation - Performance modeling */}
            <Route path="/simulation" element={<Simulation />} />

            {/* Risk - Risk assessment and management */}
            <Route path="/risk" element={<Risk />} />

            {/* BoM - Bill of Materials and procurement */}
            <Route path="/bom" element={<BoM />} />

            {/* Reports - Analytics and reporting */}
            <Route path="/reports" element={<Reports />} />
          </Routes>
        </Layout>
      </Router>
    </AppProvider>
  );
}

export default App;
