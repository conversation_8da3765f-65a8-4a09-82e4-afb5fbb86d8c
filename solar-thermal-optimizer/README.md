# SolarPro

A comprehensive web application for managing solar installations, project planning, and optimization with integrated project management capabilities.

## Features

### Core Functionality
- **Project Management**: Comprehensive solar project lifecycle management
- **Site Layout Design**: Interactive layout planning and optimization
- **Performance Simulation**: Energy yield modeling and financial analysis
- **Risk Assessment**: Risk identification, tracking, and mitigation
- **Supply Chain Management**: Material procurement and delivery tracking

### Project Management
- **Project Tracking**: Manage repair projects across multiple sites
- **Task Management**: Create, assign, and track repair tasks
- **Resource Planning**: Optimize crew allocation and scheduling
- **Progress Monitoring**: Real-time project status and completion tracking
- **Cost Management**: Budget tracking and cost analysis

### Defect Management
- **Defect Database**: Comprehensive defect tracking and history
- **Classification System**: Standardized defect categorization
- **Severity Assessment**: Automated and manual severity scoring
- **Repair Recommendations**: AI-powered repair strategy suggestions
- **Quality Assurance**: Post-repair verification workflows

## Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Routing**: React Router DOM
- **Styling**: Tailwind CSS
- **Charts**: Chart.js + Plotly.js for interactive visualizations
- **HTTP Client**: Axios for API communication
- **Icons**: Lucide React
- **Date Handling**: date-fns

## Getting Started

1. Clone the repository:
   ```bash
   git clone <your-repo-url>
   cd solar-thermal-optimizer
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Build for production:
   ```bash
   npm run build
   ```

## API Integration

The application expects a FastAPI backend with the following endpoints:

### Core Endpoints
- `GET /api/sites` - List all solar sites
- `GET /api/sites/{site_id}/flights` - Get flight dates for a site
- `GET /api/defects/{site_id}/{flight_date}` - Get defect data
- `POST /api/optimize` - Run optimization algorithm

### Project Management Endpoints
- `GET /api/projects` - List all projects
- `POST /api/projects` - Create new project
- `GET /api/projects/{project_id}/tasks` - Get project tasks
- `PUT /api/tasks/{task_id}` - Update task status

### Defect Management Endpoints
- `GET /api/defects` - List all defects with filters
- `POST /api/defects` - Create new defect record
- `PUT /api/defects/{defect_id}` - Update defect information

## Architecture

The application is built with modular, reusable components organized for scalability:

```
src/
├── components/          # Reusable UI components
├── pages/              # Main application views
├── services/           # API communication layer
├── types/              # TypeScript type definitions
├── utils/              # Helper functions and utilities
├── hooks/              # Custom React hooks
└── contexts/           # React context providers
```

## Responsive Design

Optimized for:
- Desktop (1920x1080 and above)
- Laptop (1366x768 and above)
- Tablet (768x1024 and above)

## Future Enhancements

- Map visualization integration
- Mobile app companion
- Advanced analytics dashboard
- Machine learning defect prediction
- Integration with maintenance management systems
